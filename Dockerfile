# Stage 1: Build Stage
FROM node:20.14.0 AS builder

# Set Working Directory
WORKDIR /app

# Copy Package.json and lock files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application
COPY . .

# Build the Next.js application
RUN npm run build

# Stage 2: Production Stage
FROM node:20.14.0 AS runner

# Set environment variables
ENV PORT=3000

# Set Working Directory
WORKDIR /app

# Copy only the necessary files from the build stage
COPY --from=builder /app/.env ./.env
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json

# Expose application port
EXPOSE 3000

# Start the Next.js application
CMD ["npm", "start"]
